
                        <!DOCTYPE html>
                        <html lang="en">
                        <head>
                            <meta charset="UTF-8">
                            <meta name="viewport" content="width=device-width, initial-scale=1.0">
							<style>
								body {
									background-color: white; /* Ensure the iframe has a white background */
								}

								
        /* =================================================================== */
        /* === 1. 全局设计变量 (已根据参考文件更新) === */
        /* =================================================================== */
        :root {
            /* Updated colors for light wave background */
            --text-color: #1d1d1f;
            --heading-color: #1d1d1f;
            --accent-color: #007AFF; /* Apple blue accent */
            --subtle-text-color: rgba(29, 29, 31, 0.8);

            /* Card variables adapted for light background */
            --card-bg-color: rgba(255, 255, 255, 0.8);
            --card-border-color: rgba(255, 255, 255, 0.9);
            --card-blur: 20px;
            --card-radius: 20px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            width: 100%;
            height: 100%;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif; /* Using Apple's font stack */
            color: var(--text-color);

            /* Apple Keynote-style wave background */
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            position: relative;
        }

        /* Wave animation container */
        .wave-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        /* Wave layers */
        .wave {
            position: absolute;
            top: 0;
            left: 0;
            width: 200%;
            height: 100%;
            background-repeat: repeat-x;
            animation-iteration-count: infinite;
            animation-timing-function: linear;
        }

        .wave1 {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'%3E%3Cpath d='M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z' opacity='.25' fill='%23ffffff'/%3E%3C/svg%3E");
            animation: wave1 10s ease-in-out infinite alternate;
        }

        .wave2 {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'%3E%3Cpath d='M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z' opacity='.5' fill='%23e3f2fd'/%3E%3C/svg%3E");
            animation: wave2 7s ease-in-out infinite alternate;
        }

        .wave3 {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'%3E%3Cpath d='M0,0V7.23C0,65.52,268.63,112.77,600,112.77S1200,65.52,1200,7.23V0Z' opacity='.2' fill='%23bbdefb'/%3E%3C/svg%3E");
            animation: wave3 12s ease-in-out infinite alternate;
        }

        /* Wave animations */
        @keyframes wave1 {
            0% { transform: translateX(0) translateY(0); }
            100% { transform: translateX(-50%) translateY(-10px); }
        }

        @keyframes wave2 {
            0% { transform: translateX(-25%) translateY(5px); }
            100% { transform: translateX(-75%) translateY(-5px); }
        }

        @keyframes wave3 {
            0% { transform: translateX(-50%) translateY(-5px); }
            100% { transform: translateX(0) translateY(5px); }
        }
        
        /* Main layout and transitions remain the same */
        .main {
            width: 100%;
            height: 100%;
            display: flex;
            transition: transform 1s cubic-bezier(0.86, 0, 0.07, 1);
        }

        .page {
            width: 100vw;
            height: 100vh;
            flex-shrink: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 0 5vw;
        }
        
        .page-inner {
            width: 100%;
            max-width: 1200px;
            text-align: center;
            opacity: 0;
            transform: translateY(30px);
            transition: none;
        }
        .page.active .page-inner {
            opacity: 1;
            transform: translateY(0);
            transition: opacity 0.8s ease-out 0.4s, transform 0.8s ease-out 0.4s;
        }
        
        /* =================================================================== */
        /* === 2. Card Design (已根据参考文件更新) === */
        /* =================================================================== */

        /* MODIFIED: New card style from your reference style.css */
        .card, .grid-item {
            background: var(--card-bg-color);
            backdrop-filter: blur(var(--card-blur)) saturate(180%);
            -webkit-backdrop-filter: blur(var(--card-blur)) saturate(180%);
            border-radius: var(--card-radius);
            border: 1px solid var(--card-border-color);
            box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .card { display: flex; align-items: center; text-align: left; padding: 1.5rem 2rem; }
        
        /* Retained a simple, clean hover effect */
        .card:hover, .grid-item:hover { 
            transform: translateY(-8px);
            box-shadow: 0 12px 40px 0 rgba(0, 0, 0, 0.45); 
        }

        /* All other styles remain largely the same, but adapted for the new theme */
        .content-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; width: 100%; }
        .grid-item { padding: 2rem; }
        
        h1 { font-size: 4rem; font-weight: 600; color: var(--heading-color); margin-bottom: 1rem; }
        h2 { font-size: 3rem; font-weight: 500; color: var(--heading-color); margin-bottom: 3rem; display: inline-block; border-bottom: 2px solid var(--accent-color); padding-bottom: 1rem; }
        h3 { font-size: 1.6rem; font-weight: 500; color: var(--heading-color); margin-bottom: 1rem; }
        p { font-size: 1.4rem; line-height: 1.7; color: var(--subtle-text-color); }
        .subtitle { font-size: 1.6rem; color: var(--subtle-text-color); font-weight: 300; }
        
        .card-list { list-style: none; display: flex; flex-direction: column; gap: 1.5rem; width: 100%; max-width: 900px; margin: 0 auto; }
        .card-content { font-size: 1.4rem; line-height: 1.7; color: var(--subtle-text-color); }
        .card i.fas { color: var(--accent-color); font-size: 1.8rem; margin-right: 1.5rem; width: 35px; text-align: center; }
        .highlight { color: var(--accent-color); font-weight: 500; }
        
        .grid-item i.fas { font-size: 2.5rem; margin: 0 0 1rem 0; display: block; width: auto; color: var(--accent-color); }
        
        .warning-box { background-color: var(--card-bg-color); color: var(--text-color); backdrop-filter: blur(var(--card-blur)); border: 1px solid var(--accent-color); padding: 0.8rem 1.5rem; border-radius: 8px; font-weight: 700; display: inline-flex; align-items: center; margin-top: 1rem; }
        .warning-box i { margin-right: 10px; color: var(--accent-color); }

        /* Navigation elements remain the same */
        .pagination { position: fixed; bottom: 30px; left: 50%; transform: translateX(-50%); list-style: none; z-index: 1000; display: flex; gap: 15px; padding: 8px 15px; background: rgba(0, 0, 0, 0.2); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border-radius: 20px; }
        .pagination-item { width: 12px; height: 12px; border-radius: 50%; background-color: rgba(255, 255, 255, 0.4); cursor: pointer; transition: background-color 0.3s, transform 0.3s; }
        .pagination-item.active { background-color: var(--accent-color); transform: scale(1.4); }
        .nav-button { position: fixed; top: 50%; transform: translateY(-50%); background-color: rgba(255, 255, 255, 0.1); color: white; border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 50%; width: 50px; height: 50px; font-size: 20px; cursor: pointer; z-index: 100; display: flex; justify-content: center; align-items: center; transition: background-color 0.3s, opacity 0.3s; opacity: 0.7; backdrop-filter: blur(5px); -webkit-backdrop-filter: blur(5px); }
        .nav-button:hover { background-color: rgba(255, 255, 255, 0.2); opacity: 1; }
        .nav-button.hidden { opacity: 0; pointer-events: none; }
        #prev-btn { left: 30px; }
        #next-btn { right: 30px; }

    

							</style>
                        </head>
                        <body>
                            <!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=1920, height=1080, initial-scale=1.0">
    <title>AI+Python提高工作效率 - Frosted Glass Edition</title>
    <!-- 引入Font Awesome图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        /* =================================================================== */
        /* === 1. 全局设计变量 (已根据参考文件更新) === */
        /* =================================================================== */
        :root {
            /* Updated colors for light wave background */
            --text-color: #1d1d1f;
            --heading-color: #1d1d1f;
            --accent-color: #007AFF; /* Apple blue accent */
            --subtle-text-color: rgba(29, 29, 31, 0.8);

            /* Card variables adapted for light background */
            --card-bg-color: rgba(255, 255, 255, 0.8);
            --card-border-color: rgba(255, 255, 255, 0.9);
            --card-blur: 20px;
            --card-radius: 20px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            width: 100%;
            height: 100%;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif; /* Using Apple's font stack */
            color: var(--text-color);

            /* Apple Keynote-style wave background */
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            position: relative;
        }
        
        /* Main layout and transitions remain the same */
        .main {
            width: 100%;
            height: 100%;
            display: flex;
            transition: transform 1s cubic-bezier(0.86, 0, 0.07, 1);
        }

        .page {
            width: 100vw;
            height: 100vh;
            flex-shrink: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 0 5vw;
        }
        
        .page-inner {
            width: 100%;
            max-width: 1200px;
            text-align: center;
            opacity: 0;
            transform: translateY(30px);
            transition: none;
        }
        .page.active .page-inner {
            opacity: 1;
            transform: translateY(0);
            transition: opacity 0.8s ease-out 0.4s, transform 0.8s ease-out 0.4s;
        }
        
        /* =================================================================== */
        /* === 2. Card Design (已根据参考文件更新) === */
        /* =================================================================== */

        /* MODIFIED: New card style from your reference style.css */
        .card, .grid-item {
            background: var(--card-bg-color);
            backdrop-filter: blur(var(--card-blur)) saturate(180%);
            -webkit-backdrop-filter: blur(var(--card-blur)) saturate(180%);
            border-radius: var(--card-radius);
            border: 1px solid var(--card-border-color);
            box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .card { display: flex; align-items: center; text-align: left; padding: 1.5rem 2rem; }
        
        /* Retained a simple, clean hover effect */
        .card:hover, .grid-item:hover { 
            transform: translateY(-8px);
            box-shadow: 0 12px 40px 0 rgba(0, 0, 0, 0.45); 
        }

        /* All other styles remain largely the same, but adapted for the new theme */
        .content-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; width: 100%; }
        .grid-item { padding: 2rem; }
        
        h1 { font-size: 4rem; font-weight: 600; color: var(--heading-color); margin-bottom: 1rem; }
        h2 { font-size: 3rem; font-weight: 500; color: var(--heading-color); margin-bottom: 3rem; display: inline-block; border-bottom: 2px solid var(--accent-color); padding-bottom: 1rem; }
        h3 { font-size: 1.6rem; font-weight: 500; color: var(--heading-color); margin-bottom: 1rem; }
        p { font-size: 1.4rem; line-height: 1.7; color: var(--subtle-text-color); }
        .subtitle { font-size: 1.6rem; color: var(--subtle-text-color); font-weight: 300; }
        
        .card-list { list-style: none; display: flex; flex-direction: column; gap: 1.5rem; width: 100%; max-width: 900px; margin: 0 auto; }
        .card-content { font-size: 1.4rem; line-height: 1.7; color: var(--subtle-text-color); }
        .card i.fas { color: var(--accent-color); font-size: 1.8rem; margin-right: 1.5rem; width: 35px; text-align: center; }
        .highlight { color: var(--accent-color); font-weight: 500; }
        
        .grid-item i.fas { font-size: 2.5rem; margin: 0 0 1rem 0; display: block; width: auto; color: var(--accent-color); }
        
        .warning-box { background-color: var(--card-bg-color); color: var(--text-color); backdrop-filter: blur(var(--card-blur)); border: 1px solid var(--accent-color); padding: 0.8rem 1.5rem; border-radius: 8px; font-weight: 700; display: inline-flex; align-items: center; margin-top: 1rem; }
        .warning-box i { margin-right: 10px; color: var(--accent-color); }

        /* Navigation elements remain the same */
        .pagination { position: fixed; bottom: 30px; left: 50%; transform: translateX(-50%); list-style: none; z-index: 1000; display: flex; gap: 15px; padding: 8px 15px; background: rgba(0, 0, 0, 0.2); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border-radius: 20px; }
        .pagination-item { width: 12px; height: 12px; border-radius: 50%; background-color: rgba(255, 255, 255, 0.4); cursor: pointer; transition: background-color 0.3s, transform 0.3s; }
        .pagination-item.active { background-color: var(--accent-color); transform: scale(1.4); }
        .nav-button { position: fixed; top: 50%; transform: translateY(-50%); background-color: rgba(255, 255, 255, 0.1); color: white; border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 50%; width: 50px; height: 50px; font-size: 20px; cursor: pointer; z-index: 100; display: flex; justify-content: center; align-items: center; transition: background-color 0.3s, opacity 0.3s; opacity: 0.7; backdrop-filter: blur(5px); -webkit-backdrop-filter: blur(5px); }
        .nav-button:hover { background-color: rgba(255, 255, 255, 0.2); opacity: 1; }
        .nav-button.hidden { opacity: 0; pointer-events: none; }
        #prev-btn { left: 30px; }
        #next-btn { right: 30px; }

    </style>
</head>
<body>
    <!-- Wave background container -->
    <div class="wave-container">
        <div class="wave wave1"></div>
        <div class="wave wave2"></div>
        <div class="wave wave3"></div>
    </div>

    <main class="main">
        <!-- The HTML content of all 9 pages remains exactly the same -->
        <!-- Slide 1: 封面页 -->
        <div class="page">
            <div class="page-inner">
                <h1>利用AI+Python实现工作效能新飞跃</h1>
                <p class="subtitle">拥抱变革</p>
                <p class="subtitle">演讲人：林东</p>
            </div>
        </div>
        <!-- Slide 2: 挑战与机遇 -->
        <div class="page">
            <div class="page-inner">
                <h2>挑战与机遇</h2>
                <ul class="card-list">
                    <li class="card"><i class="fas fa-cogs"></i><div class="card-content"><span class="highlight">当前痛点：</span>日常数据处理任务繁琐、重复。</div></li>
                    <li class="card"><i class="fas fa-exclamation-triangle"></i><div class="card-content"><span class="highlight">潜在风险：</span>耗费大量时间，且人工操作容易出错。</div></li>
                    <li class="card"><i class="fas fa-lightbulb"></i><div class="card-content"><span class="highlight">核心方案：</span>利用AI辅助，自动化完成Python编程。</div></li>
                    <li class="card"><i class="fas fa-rocket"></i><div class="card-content"><span class="highlight">未来机遇：</span>将我们从重复劳动中解放，聚焦更高价值的工作。</div></li>
                </ul>
            </div>
        </div>
        <!-- Slide 3: 什么是Python -->
        <div class="page">
            <div class="page-inner">
                <h2>Part 1 - 什么是Python？</h2>
                <ul class="card-list">
                    <li class="card"><i class="fas fa-code"></i><div class="card-content"><span class="highlight">强大工具：</span>一种功能强大的高级编程语言。</div></li>
                    <li class="card"><i class="fas fa-sitemap"></i><div class="card-content"><span class="highlight">应用广泛：</span>在数据分析、办公自动化、AI等领域是绝对主力。</div></li>
                    <li class="card"><i class="fas fa-book-open"></i><div class="card-content"><span class="highlight">学习成本：</span>传统方式下存在一定学习门槛，但AI正在改变这一切！</div></li>
                </ul>
            </div>
        </div>
        <!-- Slide 4: 痛点聚焦 -->
        <div class="page">
            <div class="page-inner">
                <h2>Part 2 - 痛点聚焦：数据处理</h2>
                <ul class="card-list">
                    <li class="card"><i class="fas fa-spinner fa-spin"></i><div class="card-content"><span class="highlight">繁琐耗时：</span>海量的手动复制、粘贴、核对操作。</div></li>
                    <li class="card"><i class="fas fa-times-circle"></i><div class="card-content"><span class="highlight">容易出错：</span>人工操作难以保证100%的准确性。</div></li>
                    <li class="card"><i class="fas fa-battery-quarter"></i><div class="card-content"><span class="highlight">效率低下：</span>占用了本可用于创造性工作的大量宝贵时间。</div></li>
                </ul>
            </div>
        </div>
        <!-- Slide 5: 核心解决方案 -->
        <div class="page">
            <div class="page-inner">
                <h2>Part 3 - 核心解决方案</h2>
                <p class="subtitle" style="margin-bottom: 2rem;">让AI为我们写代码</p>
                <div class="content-grid">
                    <div class="grid-item">
                        <h3>Before: 手动操作</h3>
                        <p>数小时甚至数天</p>
                        <p>易错、枯燥、效率低</p>
                    </div>
                    <div class="grid-item">
                        <h3>After: AI + Python</h3>
                        <p>几十秒代码运行</p>
                        <p>精准、高效、可复用</p>
                    </div>
                </div>
            </div>
        </div>
        <!-- Slide 6: 如何“指挥”AI -->
        <div class="page">
             <div class="page-inner">
                <h2>如何“指挥”AI？三步搞定！</h2>
                <div class="content-grid">
                    <div class="grid-item">
                        <i class="fas fa-bullseye"></i>
                        <h3>第一步：明确需求</h3>
                        <p>使用清晰的提示词描述任务，必要时上传脱敏后的文件。</p>
                    </div>
                    <div class="grid-item">
                        <i class="fas fa-comments"></i>
                        <h3>第二步：沟通迭代</h3>
                        <p>将报错信息反馈给AI，与AI反复沟通，修正Bug。</p>
                    </div>
                    <div class="grid-item">
                        <i class="fas fa-shield-alt"></i>
                        <h3>第三步：安全第一</h3>
                        <p class="warning-box"><i class="fas fa-exclamation-circle"></i>敏感数据请务必脱敏</p>
                    </div>
                </div>
            </div>
        </div>
        <!-- Slide 7: 更多可能性 -->
        <div class="page">
            <div class="page-inner">
                <h2>Part 4 - AI赋能的更多可能性</h2>
                <div class="content-grid">
                    <div class="grid-item">
                        <i class="fas fa-chart-pie"></i>
                        <h3>数据可视化</h3>
                        <p>让AI自动生成专业的分析图表，让汇报更清晰有力。</p>
                    </div>
                    <div class="grid-item">
                        <i class="fas fa-desktop"></i>
                        <h3>创意演示</h3>
                        <p>让AI编写HTML代码，制作比传统PPT更生动的动态演示。</p>
                    </div>
                    <div class="grid-item">
                        <i class="fas fa-database"></i>
                        <h3>知识管理</h3>
                        <p>用AI搭建专属知识库，实现法规、资料的秒级快速查询。</p>
                    </div>
                </div>
            </div>
        </div>
        <!-- Slide 8: 总结与展望 -->
        <div class="page">
            <div class="page-inner">
                <h2>总结：开启高效工作新模式</h2>
                <ul class="card-list">
                    <li class="card"><i class="fas fa-key"></i><div class="card-content"><span class="highlight">一把“金钥匙”：</span>AI + Python 是解放生产力的强大工具。</div></li>
                    <li class="card"><i class="fas fa-user-astronaut"></i><div class="card-content"><span class="highlight">一个身份转变：</span>从“执行者”转变为智能工具的“指挥者”。</div></li>
                    <li class="card"><i class="fas fa-forward"></i><div class="card-content"><span class="highlight">共同的未来：</span>拥抱新技术，探索AI赋能工作的无限可能。</div></li>
                </ul>
            </div>
        </div>
        <!-- Slide 9: 结束页 -->
        <div class="page">
            <div class="page-inner">
                <h1>感谢聆听！</h1>
                <p class="subtitle">Q&A</p>
            </div>
        </div>
    </main>

    <!-- 导航按钮和JS代码保持不变 -->
    <button id="prev-btn" class="nav-button hidden"><i class="fas fa-arrow-left"></i></button>
    <button id="next-btn" class="nav-button"><i class="fas fa-arrow-right"></i></button>
    <ul class="pagination"></ul>
    
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const main = document.querySelector('.main');
            const pages = document.querySelectorAll('.page');
            const paginationContainer = document.querySelector('.pagination');
            const prevBtn = document.getElementById('prev-btn');
            const nextBtn = document.getElementById('next-btn');
            
            const pageCount = pages.length;
            let activeIndex = 0;
            let isAnimating = false;

            paginationContainer.innerHTML = ''; 

            for (let i = 0; i < pageCount; i++) {
                const li = document.createElement('li');
                li.className = 'pagination-item';
                li.dataset.index = i;
                paginationContainer.appendChild(li);
            }
            const paginationItems = document.querySelectorAll('.pagination-item');

            const update = () => {
                isAnimating = true;
                main.style.transform = `translateX(-${activeIndex * 100}vw)`;
                
                pages.forEach((page, index) => {
                    page.classList.toggle('active', index === activeIndex);
                });

                paginationItems.forEach((item, index) => {
                    item.classList.toggle('active', index === activeIndex);
                });

                prevBtn.classList.toggle('hidden', activeIndex === 0);
                nextBtn.classList.toggle('hidden', activeIndex === pageCount - 1);

                setTimeout(() => {
                    isAnimating = false;
                }, 1200);
            };

            const goToPrev = () => {
                if (isAnimating || activeIndex === 0) return;
                activeIndex--;
                update();
            };

            const goToNext = () => {
                if (isAnimating || activeIndex === pageCount - 1) return;
                activeIndex++;
                update();
            };
            
            paginationItems.forEach(item => {
                item.addEventListener('click', () => {
                    if (isAnimating) return;
                    const targetIndex = parseInt(item.dataset.index);
                    if (targetIndex === activeIndex) return;
                    activeIndex = targetIndex;
                    update();
                });
            });

            document.addEventListener('keydown', (e) => {
                if (e.key === 'ArrowLeft') goToPrev();
                else if (e.key === 'ArrowRight') goToNext();
            });

            prevBtn.addEventListener('click', goToPrev);
            nextBtn.addEventListener('click', goToNext);

            update();
        });
    </script>
</body>
</html>



							<script>
                            	
        document.addEventListener('DOMContentLoaded', () => {
            const main = document.querySelector('.main');
            const pages = document.querySelectorAll('.page');
            const paginationContainer = document.querySelector('.pagination');
            const prevBtn = document.getElementById('prev-btn');
            const nextBtn = document.getElementById('next-btn');
            
            const pageCount = pages.length;
            let activeIndex = 0;
            let isAnimating = false;

            paginationContainer.innerHTML = ''; 

            for (let i = 0; i < pageCount; i++) {
                const li = document.createElement('li');
                li.className = 'pagination-item';
                li.dataset.index = i;
                paginationContainer.appendChild(li);
            }
            const paginationItems = document.querySelectorAll('.pagination-item');

            const update = () => {
                isAnimating = true;
                main.style.transform = `translateX(-${activeIndex * 100}vw)`;
                
                pages.forEach((page, index) => {
                    page.classList.toggle('active', index === activeIndex);
                });

                paginationItems.forEach((item, index) => {
                    item.classList.toggle('active', index === activeIndex);
                });

                prevBtn.classList.toggle('hidden', activeIndex === 0);
                nextBtn.classList.toggle('hidden', activeIndex === pageCount - 1);

                setTimeout(() => {
                    isAnimating = false;
                }, 1200);
            };

            const goToPrev = () => {
                if (isAnimating || activeIndex === 0) return;
                activeIndex--;
                update();
            };

            const goToNext = () => {
                if (isAnimating || activeIndex === pageCount - 1) return;
                activeIndex++;
                update();
            };
            
            paginationItems.forEach(item => {
                item.addEventListener('click', () => {
                    if (isAnimating) return;
                    const targetIndex = parseInt(item.dataset.index);
                    if (targetIndex === activeIndex) return;
                    activeIndex = targetIndex;
                    update();
                });
            });

            document.addEventListener('keydown', (e) => {
                if (e.key === 'ArrowLeft') goToPrev();
                else if (e.key === 'ArrowRight') goToNext();
            });

            prevBtn.addEventListener('click', goToPrev);
            nextBtn.addEventListener('click', goToNext);

            update();
        });
    

							</script>
                        </body>
                        </html>
                    